#!/usr/bin/env python3
"""
Test script to verify the JSON double-stringification fix.
"""
import json

def test_node_data_processing():
    """Test the node data processing logic to ensure no double-stringification."""
    
    # Simulate the original problematic logic
    def original_logic(value):
        if isinstance(value, (dict, list)):
            return json.dumps(value)
        else:
            return str(value) if value is not None else ""
    
    # Simulate the fixed logic
    def fixed_logic(value):
        if isinstance(value, (dict, list)):
            return json.dumps(value)
        elif isinstance(value, str) and value.strip() and (
            (value.strip().startswith("{") and value.strip().endswith("}")) or
            (value.strip().startswith("[") and value.strip().endswith("]"))
        ):
            # Value is already a JSON string, don't double-stringify
            return value
        else:
            return str(value) if value is not None else ""
    
    # Test cases
    test_cases = [
        # Case 1: Regular dict (should be stringified)
        {"Authorization": "Bearer"},
        
        # Case 2: Already stringified JSON (should NOT be double-stringified)
        '{"Authorization": "Bearer"}',
        
        # Case 3: Regular string (should be returned as-is)
        "simple string",
        
        # Case 4: List (should be stringified)
        ["item1", "item2"],
        
        # Case 5: Already stringified list (should NOT be double-stringified)
        '["item1", "item2"]',
        
        # Case 6: None value
        None,
        
        # Case 7: Empty string
        "",
        
        # Case 8: Whitespace-only string
        "   ",
    ]
    
    print("Testing node data processing logic...")
    print("=" * 60)
    
    for i, test_value in enumerate(test_cases, 1):
        print(f"\nTest Case {i}: {repr(test_value)}")
        print(f"Type: {type(test_value).__name__}")
        
        original_result = original_logic(test_value)
        fixed_result = fixed_logic(test_value)
        
        print(f"Original logic result: {repr(original_result)}")
        print(f"Fixed logic result:    {repr(fixed_result)}")
        
        # Check if the results are different
        if original_result != fixed_result:
            print("✅ FIXED: Results differ (fix applied)")
        else:
            print("✓ Same result (no change needed)")
    
    print("\n" + "=" * 60)
    print("Test completed!")
    
    # Specific test for the reported issue
    print("\nSpecific test for the reported issue:")
    print("-" * 40)
    
    # This is what the user entered in the inspector
    user_input = '{"Authorization": "Bearer"}'
    print(f"User input: {repr(user_input)}")
    
    original_output = original_logic(user_input)
    fixed_output = fixed_logic(user_input)
    
    print(f"Original logic output: {repr(original_output)}")
    print(f"Fixed logic output:    {repr(fixed_output)}")
    
    if original_output != fixed_output:
        print("✅ SUCCESS: The fix prevents double-stringification!")
        print(f"Original would have produced: {original_output}")
        print(f"Fixed version produces:       {fixed_output}")
    else:
        print("❌ ISSUE: Fix didn't work as expected")

if __name__ == "__main__":
    test_node_data_processing()
